import os
import sys
import tkinter as tk
from tkinter import filedialog, ttk
from datetime import datetime
import threading
import glob

# 假设 extract_patient_info.py 在同一目录下，导入核心处理函数
try:
    from extract_patient_info import extract_patient_info, save_to_csv, find_files
except ImportError:
    print("错误: 无法找到 'extract_patient_info.py'。请确保此脚本与 'run_vng_gui.py' 在同一目录下。")
    sys.exit(1)


def select_folder_and_run():
    """弹出文件夹选择对话框，然后运行信息提取。"""
    # 将默认目录设置为用户主目录，避免创建不必要的文件夹
    default_directory = os.path.expanduser("~")

    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    folder_selected = filedialog.askdirectory(
        initialdir=default_directory,
        title="选择包含PDF或DOCX文件的文件夹"
    )

    if not folder_selected:
        # 用户取消选择，静默退出
        root.destroy()
        return

    supported_files = find_files(folder_selected)
    if not supported_files:
        # 没有文件可处理，静默退出
        root.destroy()
        return

    # 创建进度条窗口
    progress_window = tk.Toplevel(root)
    progress_window.title("处理进度")
    progress_window.geometry("400x120")
    progress_window.resizable(False, False)
    
    progress_label = tk.Label(progress_window, text="准备处理...", pady=10, wraplength=380)
    progress_label.pack()

    progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=350, mode="determinate")
    progress_bar.pack(pady=5)
    
    count_label = tk.Label(progress_window, text=f"0 / {len(supported_files)}")
    count_label.pack(pady=5)

    def run_processing_in_thread():
        """在新线程中运行处理函数，避免GUI卡死"""
        log_file_name = None # 初始化日志文件名
        try:
            successful_results = []
            failed_paths = []
            ESSENTIAL_KEYS = ["姓名", "性别", "年龄", "科别", "住院号", "检查日期"]
            total_files = len(supported_files)

            for i, path in enumerate(supported_files):
                # 定义一个函数以便在主线程中安全地更新GUI
                def update_gui():
                    progress_label.config(text=f"正在处理: {os.path.basename(path)}")
                    progress_bar['value'] = i + 1
                    count_label.config(text=f"{i + 1} / {total_files}")
                
                # 将GUI更新任务推送到主线程执行
                root.after(0, update_gui)
                
                try:
                    # 直接调用核心提取函数
                    info = extract_patient_info(path, debug=False)
                    missing_keys = [key for key in ESSENTIAL_KEYS if not info.get(key)]
                    
                    if not missing_keys:
                        successful_results.append((path, info))
                    else:
                        # 如果信息不完整，记录为失败
                        failed_paths.append(os.path.abspath(path))

                except Exception:
                    # 任何处理单个文件的错误都将其归为失败
                    failed_paths.append(os.path.abspath(path))
            
            # --- 所有文件处理完毕 ---
            num_successful = len(successful_results)
            unique_failed_paths = sorted(list(set(failed_paths)))
            num_failed = len(unique_failed_paths)
            
            # 保存成功提取的结果
            if successful_results:
                save_to_csv(successful_results)
            
            # 将无法处理的文件绝对路径写入日志
            if unique_failed_paths:
                log_dir = os.path.join(os.path.dirname(__file__), "logs")
                if not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                log_file_name = f"failed_files_{timestamp}.txt"
                failed_log_path = os.path.join(log_dir, log_file_name)
                
                with open(failed_log_path, 'w', encoding='utf-8') as f:
                    f.write("以下文件未能成功处理或信息提取不完整，其绝对路径如下：\n\n")
                    for file_path in unique_failed_paths:
                        f.write(file_path + '\n')

            def show_summary():
                """在主线程中更新GUI以显示最终总结"""
                progress_window.title("处理完成")
                progress_bar.pack_forget()
                count_label.pack_forget()

                summary_message = f"处理完成！\n\n成功: {num_successful}个, 失败: {num_failed}个。"
                if num_failed > 0 and log_file_name:
                    summary_message += f"\n\n未能成功处理的文件列表见:\nlogs/{log_file_name}"

                progress_label.config(text=summary_message)
                
                # 添加确定按钮来关闭窗口
                ok_button = ttk.Button(progress_window, text="确定", command=root.destroy)
                ok_button.pack(pady=10)

            # 将GUI更新任务推送到主线程执行
            root.after(0, show_summary)

        except Exception as e:
            # 如果发生意外的严重错误，也确保能关闭窗口
            print(f"处理线程发生严重错误: {e}")
            root.after(0, root.destroy)

    # 配置进度条最大值
    progress_bar['maximum'] = len(supported_files)

    # 启动新线程进行处理
    processing_thread = threading.Thread(target=run_processing_in_thread)
    processing_thread.start()

    # 启动Tkinter事件循环
    root.mainloop()


if __name__ == "__main__":
    select_folder_and_run() 