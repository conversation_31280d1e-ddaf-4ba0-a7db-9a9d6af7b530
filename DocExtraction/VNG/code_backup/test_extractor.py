#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG数据提取器测试脚本

用于测试VNG数据提取器的功能，处理少量文件验证提取效果。

使用方法:
    python test_extractor.py
    
作者: Assistant
日期: 2025-01-02
"""

import os
import sys
from vng_data_extractor import VNGDataExtractor


def test_single_file():
    """测试单个文件的数据提取"""
    print("=== 测试单个文件数据提取 ===")
    
    # 测试文件路径
    test_file = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified/2018/146 梁趣轩.docx"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    # 创建临时提取器
    output_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/test_output"
    extractor = VNGDataExtractor("", output_dir, debug=True)
    
    # 提取数据
    try:
        patient_data = extractor.extract_patient_data(test_file)
        
        if patient_data:
            print("提取成功！数据如下：")
            print("-" * 50)
            for key, value in patient_data.items():
                if key != '原始文本':  # 跳过原始文本显示
                    print(f"{key}: {value}")
            print("-" * 50)
            
            # 验证数据
            is_valid, missing_fields = extractor.validate_data(patient_data)
            print(f"数据验证: {'通过' if is_valid else '失败'}")
            if not is_valid:
                print(f"缺失字段: {missing_fields}")
        else:
            print("提取失败")
            
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        print(traceback.format_exc())


def test_batch_processing():
    """测试批量处理（少量文件）"""
    print("\n=== 测试批量处理（前10个文件） ===")
    
    source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified/2018"
    output_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/test_output"
    
    if not os.path.exists(source_dir):
        print(f"测试目录不存在: {source_dir}")
        return
    
    # 获取前10个文件
    test_files = []
    for file in os.listdir(source_dir):
        if file.endswith(('.docx', '.doc', '.wps')):
            test_files.append(os.path.join(source_dir, file))
            if len(test_files) >= 10:
                break
    
    if not test_files:
        print("未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 创建提取器
    extractor = VNGDataExtractor("", output_dir, debug=False)
    
    # 手动处理文件
    successful_data = []
    
    for i, file_path in enumerate(test_files, 1):
        filename = os.path.basename(file_path)
        print(f"[{i}/{len(test_files)}] 处理: {filename}")
        
        try:
            patient_data = extractor.extract_patient_data(file_path)
            
            if patient_data:
                is_valid, missing_fields = extractor.validate_data(patient_data)
                if is_valid:
                    successful_data.append(patient_data)
                    print(f"  ✓ 成功提取: {patient_data.get('姓名', 'N/A')}")
                else:
                    print(f"  ✗ 数据不完整: 缺失 {missing_fields}")
            else:
                print(f"  ✗ 提取失败")
                
        except Exception as e:
            print(f"  ✗ 异常: {e}")
    
    print(f"\n批量测试结果:")
    print(f"总文件数: {len(test_files)}")
    print(f"成功提取: {len(successful_data)}")
    print(f"成功率: {len(successful_data)/len(test_files)*100:.1f}%")
    
    # 写入测试CSV
    if successful_data:
        csv_headers = [
            '文件名', '年份', '姓名', '性别', '年龄', '检查日期', '科别', '门诊住院号', '编号',
            '定标试验', '自发性眼震', '自发性眼震_水平向左', '自发性眼震_水平向右', 
            '自发性眼震_垂直向上', '自发性眼震_垂直向下', '凝视试验', '凝视试验_定向', 
            '凝视试验_变向', '平滑跟踪', '扫视试验', '扫视试验_详情', '视动性眼震',
            'Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象',
            '印象', '检查者', '原始文本'
        ]
        
        extractor.csv_file = os.path.join(output_dir, 'test_results.csv')
        extractor.write_csv(successful_data, csv_headers)
        print(f"测试结果已保存: {extractor.csv_file}")


def test_text_extraction():
    """测试文本提取功能"""
    print("\n=== 测试文本提取功能 ===")
    
    test_files = [
        "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified/2018/146 梁趣轩.docx",
        "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified/2020/2447 张文.docx"
    ]
    
    extractor = VNGDataExtractor("", "", debug=True)
    
    for file_path in test_files:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            print(f"\n处理文件: {filename}")
            print("-" * 30)
            
            text = extractor.extract_text_from_file(file_path)
            if text:
                print(f"提取文本长度: {len(text)} 字符")
                print("文本前200字符:")
                print(text[:200])
                print("...")
                
                # 测试各部分提取
                basic_info = extractor.extract_basic_info(text)
                print(f"\n基本信息: {basic_info}")
                
                exam_results = extractor.extract_examination_results(text)
                print(f"检查结果: {exam_results}")
                
                position_tests = extractor.extract_position_tests(text)
                print(f"位置试验: {position_tests}")
                
                diagnosis = extractor.extract_diagnosis(text)
                print(f"诊断信息: {diagnosis}")
            else:
                print("文本提取失败")
        else:
            print(f"文件不存在: {file_path}")


def main():
    """主测试函数"""
    print("VNG数据提取器测试")
    print("=" * 50)
    
    # 创建测试输出目录
    output_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 运行各项测试
        test_single_file()
        test_batch_processing()
        test_text_extraction()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())


if __name__ == "__main__":
    main()
