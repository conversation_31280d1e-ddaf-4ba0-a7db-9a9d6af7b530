#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据清理脚本
用于清理CSV数据中的格式问题和乱码
"""

import pandas as pd
import re
import os
from datetime import datetime

class DataCleaner:
    def __init__(self):
        self.corruption_patterns = [
            # 常见的乱码模式
            r'ࠀ[ࠀ-࿿]+',  # 阿拉伯文乱码
            r'[ᄀ-ᇿ]+',    # 韩文乱码
            r'[䀀-䶿]+',    # CJK扩展A区乱码
            r'[一-龯]{0,2}[ࠀ-࿿][一-龯]{0,2}',  # 混合乱码
            r'[ᘀ-ᘿ]+',    # 加拿大原住民音节乱码
            r'[㸀-㿿]+',    # CJK符号乱码
            r'[Ā-ſ]{5,}',  # 拉丁扩展乱码
            r'[Ĩ-ſ]{3,}',  # 特定拉丁字符乱码
            r'[ÿ]{2,}',    # 重复的特殊字符
            r'[搒摧桤愀]{3,}',  # 特定中文字符重复乱码
            r'[혈鐇鐏鐐]{2,}',  # 特定符号乱码
            r'[ӿ]{3,}',    # 西里尔字符乱码
            r'[Ĥ]{2,}',    # 重复的拉丁字符
            r'[ᡊ伀儀帀]{2,}',  # 特定Unicode乱码
            r'[瀁桰漀]{2,}',   # 特定中文乱码
        ]
    
    def clean_text_field(self, text):
        """清理文本字段中的乱码和多余符号"""
        if pd.isna(text) or text == '':
            return ''
        
        text = str(text)
        
        # 移除乱码
        for pattern in self.corruption_patterns:
            text = re.sub(pattern, '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 如果清理后文本太短或全是乱码，返回空字符串
        if len(text) < 2:
            return ''
        
        # 检查是否还有大量非中文字符（可能是乱码）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(text)
        if total_chars > 10 and chinese_chars / total_chars < 0.3:
            # 如果中文字符比例太低，可能是乱码
            return ''
        
        return text
    
    def clean_name_field(self, name):
        """清理姓名字段"""
        if pd.isna(name) or name == '':
            return ''
        
        name = str(name).strip()
        
        # 移除前缀符号
        name = re.sub(r'^[：:_\s]+', '', name)
        
        # 移除后缀符号
        name = re.sub(r'[_\s]+$', '', name)
        
        # 只保留中文字符和常见姓名字符
        name = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf·]', '', name)
        
        return name.strip()
    
    def clean_department_field(self, dept):
        """清理科别字段"""
        if pd.isna(dept) or dept == '':
            return ''
        
        dept = str(dept).strip()
        
        # 移除前缀符号
        dept = re.sub(r'^[：:_\s]+', '', dept)
        
        # 移除后缀符号和床位信息
        dept = re.sub(r'[_\s]*\d+床[_\s]*$', '', dept)
        dept = re.sub(r'[_\s]+$', '', dept)
        
        return dept.strip()
    
    def clean_hospital_id(self, hospital_id):
        """清理住院号字段"""
        if pd.isna(hospital_id) or hospital_id == '':
            return ''
        
        hospital_id = str(hospital_id).strip()
        
        # 只保留字母数字
        hospital_id = re.sub(r'[^A-Za-z0-9]', '', hospital_id)
        
        return hospital_id
    
    def standardize_date(self, date_str):
        """标准化日期格式"""
        if pd.isna(date_str) or date_str == '':
            return ''
        
        date_str = str(date_str).strip()
        
        # 处理各种日期格式
        date_patterns = [
            (r'(\d{1,2})/(\d{1,2})/(\d{4})', r'\3-\1-\2'),  # DD/MM/YYYY -> YYYY-MM-DD
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1-\2-\3'),  # 已经是标准格式
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'), # YYYY.MM.DD -> YYYY-MM-DD
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),  # YYYY/MM/DD -> YYYY-MM-DD
        ]
        
        for pattern, replacement in date_patterns:
            if re.match(pattern, date_str):
                date_str = re.sub(pattern, replacement, date_str)
                break
        
        # 确保月份和日期是两位数
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            try:
                month = f"{int(month):02d}"
                day = f"{int(day):02d}"
                return f"{year}-{month}-{day}"
            except ValueError:
                return date_str
        
        return date_str
    
    def clean_impression_field(self, impression):
        """清理印象字段中的乱码"""
        if pd.isna(impression) or impression == '':
            return ''
        
        impression = str(impression)
        
        # 首先尝试找到乱码开始的位置
        # 通常乱码会在正常文本后突然出现
        corruption_start = None
        
        # 查找乱码模式的开始位置
        for pattern in self.corruption_patterns:
            match = re.search(pattern, impression)
            if match:
                if corruption_start is None or match.start() < corruption_start:
                    corruption_start = match.start()
        
        # 如果找到乱码，截取乱码前的部分
        if corruption_start is not None:
            # 向前查找最近的句号、逗号或其他标点作为截断点
            clean_end = corruption_start
            for i in range(corruption_start - 1, -1, -1):
                if impression[i] in '。，；、':
                    clean_end = i + 1
                    break
                elif impression[i] in '.,;':
                    clean_end = i + 1
                    break
            
            impression = impression[:clean_end]
        
        # 移除剩余的乱码
        for pattern in self.corruption_patterns:
            impression = re.sub(pattern, '', impression)
        
        # 清理多余的空白字符
        impression = re.sub(r'\s+', ' ', impression).strip()
        
        # 移除末尾的不完整句子（如果以乱码字符结尾）
        impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】]+$', '', impression)
        
        return impression.strip()
    
    def clean_csv_data(self, input_file, output_file):
        """清理整个CSV文件的数据"""
        print(f"读取文件: {input_file}")
        df = pd.read_csv(input_file)
        
        print(f"原始数据: {len(df)} 行")
        
        # 清理各个字段
        if '姓名' in df.columns:
            print("清理姓名字段...")
            df['姓名'] = df['姓名'].apply(self.clean_name_field)
        
        if '科别' in df.columns:
            print("清理科别字段...")
            df['科别'] = df['科别'].apply(self.clean_department_field)
        
        if '门诊住院号' in df.columns:
            print("清理住院号字段...")
            df['门诊住院号'] = df['门诊住院号'].apply(self.clean_hospital_id)
        
        if '检查日期' in df.columns:
            print("标准化检查日期...")
            df['检查日期'] = df['检查日期'].apply(self.standardize_date)
        
        if '印象' in df.columns:
            print("清理印象字段...")
            df['印象'] = df['印象'].apply(self.clean_impression_field)

        # 移除年份列（如果存在）
        if '年份' in df.columns:
            print("移除年份列...")
            df = df.drop('年份', axis=1)
            print(f"已移除年份列，当前列数: {len(df.columns)}")

        # 保存清理后的数据
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"清理后的数据已保存到: {output_file}")
        
        # 统计清理效果
        self.print_cleaning_stats(df)
        
        return df
    
    def print_cleaning_stats(self, df):
        """打印清理统计信息"""
        print("\n清理统计:")
        
        if '姓名' in df.columns:
            empty_names = df['姓名'].isna().sum() + (df['姓名'] == '').sum()
            print(f"  姓名字段: {len(df) - empty_names}/{len(df)} 有效")
        
        if '科别' in df.columns:
            empty_depts = df['科别'].isna().sum() + (df['科别'] == '').sum()
            print(f"  科别字段: {len(df) - empty_depts}/{len(df)} 有效")
        
        if '门诊住院号' in df.columns:
            empty_ids = df['门诊住院号'].isna().sum() + (df['门诊住院号'] == '').sum()
            print(f"  住院号字段: {len(df) - empty_ids}/{len(df)} 有效")
        
        if '印象' in df.columns:
            empty_impressions = df['印象'].isna().sum() + (df['印象'] == '').sum()
            print(f"  印象字段: {len(df) - empty_impressions}/{len(df)} 有效")

def main():
    """主函数"""
    cleaner = DataCleaner()
    
    # 输入和输出文件路径
    input_file = '../output/vng_patient_data_clean_20250802_214545.csv'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'../output/vng_patient_data_cleaned_{timestamp}.csv'
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    # 执行清理
    cleaner.clean_csv_data(input_file, output_file)
    
    print("\n数据清理完成!")

if __name__ == "__main__":
    main()
