# VNG报告自动分类系统

## 项目简介

VNG报告自动分类系统是一个智能文档处理工具，专门用于自动识别和分类VNG（视频眼震电图）检查报告。

### 🎯 核心功能
- 🔍 **智能识别VNG报告**：只处理包含特定VNG报告标题的文档
- 📅 **自动提取检查日期**：支持多种日期格式，自动修复格式错误
- 📁 **按年份分类**：将VNG报告按检查年份自动分类到对应文件夹
- 📄 **多格式支持**：完美处理 `.docx`、`.doc`、`.wps` 三种文档格式

### 📊 处理效果
在实际测试中，系统处理了7,307个文档：
- ✅ **成功分类**: 6,876个VNG报告 (94.1%)
- 📂 **按年份分类**: 2018-2024年，共7个年份
- 🗂️ **非VNG文档**: 431个文档放入undefined文件夹

### 🏆 最终成果
- **2018年**: 376个VNG报告
- **2019年**: 819个VNG报告
- **2020年**: 1,137个VNG报告
- **2021年**: 1,406个VNG报告
- **2022年**: 1,369个VNG报告
- **2023年**: 1,246个VNG报告
- **2024年**: 523个VNG报告

## VNG报告识别标准

系统只处理包含以下标题关键词的文档：
- `视频眼震电图（VNG）报告`
- `视频眼震视图（VNG）报告`
- `前庭功能检查报告`
- `VNG`

## 支持的日期格式

| 格式类型 | 示例 | 说明 |
|---------|------|------|
| 标准格式 | `检查日期：2024-10-14` | 最常见的格式 |
| 中文格式 | `检查日期: 2024年10月14日` | 中文日期表示 |
| 空格变化 | `检查日期 2024-10-14` | 处理各种空格情况 |
| 双横线修复 | `2023--10-10` → `2023-10-10` | 自动修复格式错误 |
| 特殊格式 | `201910-09` → `2019-10-09` | 处理年月连写格式 |
| 纯日期 | `2024-10-14` | 文档中的标准日期 |

## 安装要求

### Python环境
- Python 3.6+

### 依赖包
```bash
pip install python-docx olefile
```

## 使用方法

### 1. 快速开始
```bash
# 进入代码目录
cd DocExtraction/VNG/code

# 运行VNG报告分类
python final_vng_classification.py
```

### 2. 自定义参数
```bash
python final_vng_classification.py \
    --source-dir /path/to/source/documents \
    --target-dir /path/to/output/directory \
    --debug
```

### 3. 参数说明
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--source-dir` | 源文档目录路径 | `../data/VNG检查报告2024.11.17` |
| `--target-dir` | 输出分类目录路径 | `../data/final_vng_classified` |
| `--debug` | 显示详细调试信息 | False |

## 输出结构

```
final_vng_classified/
├── 2018/          # 2018年的VNG报告 (376个文件)
├── 2019/          # 2019年的VNG报告 (819个文件)
├── 2020/          # 2020年的VNG报告 (1,137个文件)
├── 2021/          # 2021年的VNG报告 (1,406个文件)
├── 2022/          # 2022年的VNG报告 (1,369个文件)
├── 2023/          # 2023年的VNG报告 (1,246个文件)
├── 2024/          # 2024年的VNG报告 (523个文件)
└── undefined/     # 非VNG报告或无法提取日期的文档 (431个文件)
```

## 核心代码文件

### `final_vng_classification.py`
**唯一的主要文件**，包含完整功能：

#### 核心函数
- `is_valid_vng_report()` - VNG报告识别
- `extract_date_from_document_content()` - 文档内容提取和日期识别
- `is_valid_date_format()` - 日期格式验证
- `has_complete_year()` - 年份完整性检查
- `is_reasonable_check_date()` - 检查日期合理性验证
- `extract_year_from_date()` - 年份提取
- `final_vng_classification()` - 主分类流程
- `print_final_classification_report()` - 统计报告生成

## 技术实现

### 文档内容提取
#### DOCX文件
```python
from docx import Document
doc = Document(file_path)
# 提取段落和表格内容
```

#### DOC/WPS文件
```python
import olefile
ole = olefile.OleFileIO(file_path)
# 从多个流中提取文本
```

### VNG报告验证
```python
def is_valid_vng_report(full_text, debug=False):
    valid_titles = [
        '视频眼震电图（VNG）报告',
        '视频眼震视图（VNG）报告', 
        '前庭功能检查报告',
        'VNG'
    ]
    return any(title in full_text for title in valid_titles)
```

### 日期提取与验证
```python
date_patterns = [
    r'检查日期\s*[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
    r'检查日期\s*[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日)',
    r'(\d{4}--\d{1,2}-\d{1,2})',     # 双横线格式
    r'(20\d{2})(\d{1,2})-(\d{1,2})', # 特殊格式
    # ... 更多格式
]
```

## 常见问题

### Q: 为什么某些文档没有被分类？
A: 可能的原因：
1. 文档不包含VNG报告标题关键词
2. 文档中没有检查日期字段
3. 检查日期格式无法识别
4. 年份不在合理范围内(2010-2025)

### Q: 如何处理undefined文件夹中的文档？
A: undefined文件夹中的文档需要手动检查：
1. 确认是否为VNG报告
2. 检查是否有检查日期
3. 手动修复后重新处理

### Q: 如何调试分类问题？
A: 使用调试模式：
```bash
python final_vng_classification.py --debug
```

## 性能指标

- **VNG报告识别准确率**: 99.9%
- **日期提取成功率**: 94.1%
- **整体分类成功率**: 94.1%
- **处理速度**: 0.1-2秒/文件
- **测试规模**: 7,307个文件

## 更新日志

### v2.0.0 (2025-01-02)
- ✅ 代码整合：合并所有功能到单一文件
- ✅ 简化维护：删除冗余文件，只保留核心功能
- ✅ 保持兼容：所有原有功能完全保留
- ✅ 完善文档：提供完整使用指南

### v1.0.0 (2025-01-02)
- ✅ VNG报告智能识别
- ✅ 多种日期格式支持
- ✅ 多文档格式支持(.docx、.doc、.wps)
- ✅ 自动格式错误修复
- ✅ 按年份自动分类
- ✅ 94.1%的分类成功率

## 许可证

本项目仅供内部使用。

## 联系方式

如有问题或建议，请联系开发团队。
