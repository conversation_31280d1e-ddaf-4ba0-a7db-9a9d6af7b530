from tqdm import tqdm
import torch
from typing import List

print("Train module loaded.")

def train_model(model, data_loader, device, num_epochs, criterion, optimizer, model_type='legacy'):
    """
    Train the model for specified number of epochs.

    Args:
        model: The model to train
        data_loader: DataLoader providing training data
        device: Device to run training on
        num_epochs: Number of training epochs
        criterion: Loss function
        optimizer: Optimizer
        model_type: 'legacy' for old models, 'bppv_transformer' for new transformer model
    """
    model.train()

    for epoch in range(num_epochs):
        print(f"Starting epoch {epoch + 1}/{num_epochs}...")

        epoch_loss = 0
        correct_predictions = 0
        total_samples = 0

        with tqdm(data_loader, desc=f"Epoch {epoch + 1}/{num_epochs}", unit="batch") as pbar:
            for batch_data in pbar:
                if model_type == 'bppv_transformer':
                    # New format: (list of 4 video tensors, labels)
                    video_list, labels = batch_data

                    # Move videos to device
                    video_list = [video.to(device) for video in video_list]
                    labels = labels.to(device)

                    # Forward pass
                    outputs = model(video_list)
                    loss = criterion(outputs, labels)

                    # Calculate accuracy
                    _, predicted = torch.max(outputs.data, 1)
                    correct_predictions += (predicted == labels).sum().item()
                    total_samples += labels.size(0)

                else:
                    # Legacy format: (videos, labels)
                    videos, labels = batch_data
                    videos = videos.to(device)

                    if criterion.__class__.__name__ == 'BCEWithLogitsLoss':
                        # Binary classification
                        labels = labels.type(torch.float32).unsqueeze(1).to(device)
                    else:
                        # Multi-class classification
                        labels = labels.to(device)

                    # Forward pass
                    outputs = model(videos)
                    loss = criterion(outputs, labels)

                    # Calculate accuracy
                    if criterion.__class__.__name__ == 'BCEWithLogitsLoss':
                        predicted = (torch.sigmoid(outputs) > 0.5).float()
                        correct_predictions += (predicted == labels).sum().item()
                    else:
                        _, predicted = torch.max(outputs.data, 1)
                        correct_predictions += (predicted == labels).sum().item()
                    total_samples += labels.size(0)

                epoch_loss += loss.item()

                # Backward pass and optimization
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                # Update progress bar
                current_accuracy = correct_predictions / total_samples * 100
                pbar.set_postfix({
                    "Loss": f"{loss.item():.4f}",
                    "Acc": f"{current_accuracy:.2f}%"
                })

        # Calculate epoch statistics
        avg_loss = epoch_loss / len(data_loader)
        epoch_accuracy = correct_predictions / total_samples * 100

        print(f"Epoch [{epoch + 1}/{num_epochs}]")
        print(f"  Average Loss: {avg_loss:.4f}")
        print(f"  Training Accuracy: {epoch_accuracy:.2f}%")
        print("-" * 50)

    print("Training completed!")


def train_four_class_model(model, data_loader, device, num_epochs, criterion, optimizer):
    """
    Specialized training function for four-class BPPV classification.
    This is a wrapper around train_model with appropriate settings.
    """
    return train_model(
        model=model,
        data_loader=data_loader,
        device=device,
        num_epochs=num_epochs,
        criterion=criterion,
        optimizer=optimizer,
        model_type='bppv_transformer'
    )
