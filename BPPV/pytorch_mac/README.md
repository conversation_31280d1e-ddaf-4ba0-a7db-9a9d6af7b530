# BPPV四分类系统

基于Transformer的BPPV（良性阵发性位置性眩晕）四分类深度学习系统。

## 系统概述

本系统实现了BPPV的四分类诊断，能够区分以下四种类型：
- **horizontal_left** (0): 左水平半规管管石症
- **horizontal_right** (1): 右水平半规管管石症  
- **posterior_left** (2): 左后半规管管石症
- **posterior_right** (3): 右后半规管管石症

## 核心特性

### ✅ 数据处理
- **多视频融合**: 每个样本由4个视频组成（DHL, DHR, ROL, ROR）
- **灰度转换**: 自动将彩色视频转换为单通道灰度
- **分辨率优化**: 自动缩小到原分辨率的1/10，提高处理效率
- **错误检测**: 自动检测缺失的视频类型并报错

### ✅ 模型架构
- **Transformer基础**: 基于预训练Vision Transformer
- **变长序列支持**: 支持不同长度的视频序列（带mask处理）
- **位置编码**: 保留视频的时序信息
- **双模式支持**: 
  - `feature_concat`: 特征维度拼接
  - `separate_fusion`: 分别处理后融合

### ✅ 训练与评估
- **四分类训练**: 使用CrossEntropyLoss
- **综合评估**: 包含准确率、混淆矩阵、分类报告
- **可视化支持**: 自动生成混淆矩阵图表
- **进度监控**: 实时显示训练进度和指标

## 文件结构

```
pytorch_mac/
├── config.py          # 配置文件
├── data.py            # 数据加载器
├── models.py          # 模型定义
├── train.py           # 训练代码
├── evaluate.py        # 评估代码
├── main.py            # 主程序
├── utils.py           # 工具函数
├── minimal_test.py    # 轻量测试脚本
└── README.md          # 本文档
```

## 数据格式

### 目录结构
```
BPPV_split/
├── horizontal_left/
│   ├── horizontal_left.txt
│   └── 患者目录/
│       ├── DHL/DHL-1.mp4
│       ├── DHR/DHR-1.mp4
│       ├── ROL/ROL-1.mp4
│       └── ROR/ROR-1.mp4
├── horizontal_right/
├── posterior_left/
└── posterior_right/
```

### 标注文件格式
每个类别的txt文件包含该类别下所有视频的路径，每4行为一组：
```
DHL	horizontal_left/患者A/DHL/DHL-1.mp4
DHR	horizontal_left/患者A/DHR/DHR-1.mp4
ROL	horizontal_left/患者A/ROL/ROL-1.mp4
ROR	horizontal_left/患者A/ROR/ROR-1.mp4
```

## 安装依赖

```bash
pip install torch torchvision transformers scikit-learn seaborn matplotlib decord opencv-python
```

## 使用方法

### 基本训练

```bash
# 特征拼接模式（推荐）
python main.py --mode four_class --concatenate_mode feature_concat

# 分别处理融合模式
python main.py --mode four_class --concatenate_mode separate_fusion
```

### 完整参数

```bash
python main.py \
    --mode four_class \
    --concatenate_mode feature_concat \
    --batch_size 2 \
    --num_epochs 10 \
    --learning_rate 0.001 \
    --max_frames 2000 \
    --save_plots
```

### 仅评估模式

```bash
python main.py \
    --mode four_class \
    --concatenate_mode feature_concat \
    --eval_only \
    --model_path path/to/saved/model.pth \
    --save_plots
```

## 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--mode` | `four_class` | 分类模式：`binary` 或 `four_class` |
| `--concatenate_mode` | `feature_concat` | 视频融合模式：`feature_concat` 或 `separate_fusion` |
| `--batch_size` | `2` | 批次大小 |
| `--num_epochs` | `10` | 训练轮数 |
| `--learning_rate` | `0.001` | 学习率 |
| `--max_frames` | `2000` | 每个视频的最大帧数 |
| `--eval_only` | `False` | 仅评估模式 |
| `--save_plots` | `False` | 保存评估图表 |
| `--model_path` | `None` | 预训练模型路径 |

## 配置文件

主要配置在 `config.py` 中：

```python
# 分类设置
NUM_CLASSES = 4
CLASS_NAMES = ['horizontal_left', 'horizontal_right', 'posterior_left', 'posterior_right']

# 数据路径
DATA_ROOT = '/path/to/BPPV_split'
CLASS_FILES = {
    'horizontal_left': f'{DATA_ROOT}/horizontal_left.txt',
    'horizontal_right': f'{DATA_ROOT}/horizontal_right.txt',
    'posterior_left': f'{DATA_ROOT}/posterior_left.txt',
    'posterior_right': f'{DATA_ROOT}/posterior_right.txt'
}

# 视频处理
RESIZE_FACTOR = 0.1  # 缩放到1/10
CONVERT_TO_GRAYSCALE = True  # 转灰度

# 模型设置
CONCATENATE_MODE = 'feature_concat'  # 默认融合模式
```

## 两种融合模式对比

### Feature Concat 模式
- **原理**: 在每个时间步将4个视频的特征拼接
- **优点**: 能够捕捉视频间的同步关系
- **适用**: 视频长度相近的情况
- **计算**: 相对较快

### Separate Fusion 模式  
- **原理**: 分别处理4个视频，最后融合表示
- **优点**: 能够处理不同长度的视频
- **适用**: 视频长度差异较大的情况
- **计算**: 相对较慢但更灵活

## 输出结果

训练完成后会输出：
- 总体准确率
- 各类别准确率
- 混淆矩阵
- 分类报告
- 模型保存路径

示例输出：
```
FOUR-CLASS BPPV CLASSIFICATION EVALUATION RESULTS
============================================================
Overall Accuracy: 0.8500 (85.00%)

Classification Report:
                    precision    recall  f1-score   support
    horizontal_left       0.82      0.85      0.83         5
   horizontal_right       0.88      0.82      0.85         5
     posterior_left       0.85      0.88      0.86         5
    posterior_right       0.85      0.85      0.85         5

Per-class Accuracy:
  horizontal_left: 0.8500 (85.00%)
  horizontal_right: 0.8200 (82.00%)
  posterior_left: 0.8800 (88.00%)
  posterior_right: 0.8500 (85.00%)
```

## 快速测试

使用轻量测试脚本验证系统：

```bash
python minimal_test.py
```

## 注意事项

1. **硬件要求**: 建议使用GPU进行训练，CPU处理视频较慢
2. **内存需求**: 根据视频大小和批次大小调整，建议从小批次开始
3. **数据完整性**: 确保每个患者都有完整的4个视频文件
4. **路径配置**: 修改 `config.py` 中的数据路径以匹配您的数据位置

## 故障排除

### 常见问题

1. **内存不足**: 减小 `batch_size` 或 `max_frames`
2. **视频文件缺失**: 检查数据目录结构和txt文件内容
3. **CUDA错误**: 确保PyTorch CUDA版本与显卡驱动兼容
4. **依赖包问题**: 按照安装依赖部分重新安装

### 性能优化

1. **减少帧数**: 降低 `max_frames` 参数
2. **增大缩放**: 增大 `RESIZE_FACTOR`（如0.2）
3. **使用GPU**: 确保CUDA可用
4. **批次调优**: 根据显存调整 `batch_size`

## 开发信息

- **开发时间**: 2025年7月
- **框架**: PyTorch + Transformers
- **支持**: 四分类BPPV诊断
- **特色**: 多视频融合 + Transformer架构
