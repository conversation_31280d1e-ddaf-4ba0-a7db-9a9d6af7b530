import os
import decord
import numpy as np
import torch
from torch.utils.data import Dataset
import cv2
from typing import List, Tuple, Dict

print("Data module loaded.")

class VideoDataset(Dataset):
    def __init__(self, normal_file_path, bppv_file_path, max_frames):
        self.video_paths = []
        self.labels = []
        self.max_frames = max_frames
        self.load_videos(normal_file_path, label=0)
        self.load_videos(bppv_file_path, label=1)

    def load_videos(self, file_path, label):
        with open(file_path, 'r') as file:
            for line in file:
                parts = line.strip().split(' ')
                if len(parts) >= 3:
                    video_path = ' '.join(parts[2:])
                    if os.path.exists(video_path):
                        self.video_paths.append(video_path)
                        self.labels.append(label)
                    else:
                        print(f"Warning: Video file does not exist - {video_path}")

    def __len__(self):
        return len(self.video_paths)

    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        label = self.labels[idx]
        try:
            vr = decord.VideoReader(video_path)
            frames = vr[:].asnumpy()
            frames = np.transpose(frames, (0, 3, 1, 2))[:, :, :, :320]
            frames = frames[::3]

            if len(frames) > self.max_frames:
                frames = frames[:self.max_frames]
            else:
                padding = np.zeros((self.max_frames - len(frames), 3, frames.shape[2], frames.shape[3]), dtype=np.float32)
                frames = np.concatenate((frames, padding), axis=0)

            frames = np.transpose(frames, (1, 0, 2, 3))
            return torch.from_numpy(frames).float(), label
        except Exception as e:
            print(f"Error reading video - {video_path}: {e}")
            raise RuntimeError("Error encountered while processing video, operation failed.")


class BPPVFourClassDataset(Dataset):
    """
    Dataset for BPPV four-class classification.
    Each sample consists of 4 videos (DHL, DHR, ROL, ROR) from the same patient.

    Class encoding:
    0: horizontal_left
    1: horizontal_right
    2: posterior_left
    3: posterior_right
    """

    def __init__(self, class_files: Dict[str, str], data_root: str, max_frames: int,
                 resize_factor: float = 0.1, convert_to_grayscale: bool = True):
        """
        Args:
            class_files: Dictionary mapping class names to txt file paths
            data_root: Root directory containing the data
            max_frames: Maximum number of frames per video
            resize_factor: Factor to resize video frames (e.g., 0.1 for 1/10 size)
            convert_to_grayscale: Whether to convert videos to grayscale
        """
        self.class_files = class_files
        self.data_root = data_root
        self.max_frames = max_frames
        self.resize_factor = resize_factor
        self.convert_to_grayscale = convert_to_grayscale

        # Class name to label mapping
        self.class_to_label = {
            'horizontal_left': 0,
            'horizontal_right': 1,
            'posterior_left': 2,
            'posterior_right': 3
        }

        self.samples = []  # List of (video_group, label) tuples
        self._load_samples()

    def _load_samples(self):
        """Load all video groups from txt files."""
        for class_name, file_path in self.class_files.items():
            label = self.class_to_label[class_name]

            with open(file_path, 'r') as f:
                lines = [line.strip() for line in f if line.strip()]

            # Group every 4 lines (DHL, DHR, ROL, ROR) into one sample
            for i in range(0, len(lines), 4):
                if i + 3 < len(lines):  # Ensure we have all 4 videos
                    video_group = []
                    video_types = []

                    for j in range(4):
                        parts = lines[i + j].split('\t')
                        if len(parts) >= 2:
                            video_type = parts[0]  # DHL, DHR, ROL, ROR
                            video_path = os.path.join(self.data_root, parts[1])

                            # Check if video file exists
                            if not os.path.exists(video_path):
                                raise FileNotFoundError(f"Video file not found: {video_path}")

                            video_group.append(video_path)
                            video_types.append(video_type)
                        else:
                            raise ValueError(f"Invalid line format in {file_path}: {lines[i + j]}")

                    # Verify we have all 4 required video types
                    expected_types = {'DHL', 'DHR', 'ROL', 'ROR'}
                    if set(video_types) != expected_types:
                        raise ValueError(f"Missing video types for sample. Expected {expected_types}, got {set(video_types)}")

                    self.samples.append((video_group, label))
                else:
                    print(f"Warning: Incomplete video group in {file_path}, skipping last {len(lines) - i} lines")

        print(f"Loaded {len(self.samples)} samples across {len(self.class_files)} classes")

    def _process_video(self, video_path: str) -> np.ndarray:
        """
        Process a single video: load, resize, convert to grayscale, and pad/truncate.

        Returns:
            np.ndarray: Processed video frames with shape (max_frames, channels, height, width)
        """
        try:
            vr = decord.VideoReader(video_path)
            frames = vr[:].asnumpy()  # Shape: (num_frames, height, width, channels)

            # Convert to grayscale if required
            if self.convert_to_grayscale:
                # Convert RGB to grayscale using standard weights
                if frames.shape[-1] == 3:
                    frames = np.dot(frames[..., :3], [0.299, 0.587, 0.114])
                    frames = np.expand_dims(frames, axis=-1)  # Add channel dimension
                channels = 1
            else:
                channels = frames.shape[-1]

            # Resize frames
            if self.resize_factor != 1.0:
                new_height = int(frames.shape[1] * self.resize_factor)
                new_width = int(frames.shape[2] * self.resize_factor)

                resized_frames = []
                for frame in frames:
                    if self.convert_to_grayscale:
                        frame = frame.squeeze(-1)  # Remove channel dim for cv2
                        resized_frame = cv2.resize(frame, (new_width, new_height))
                        resized_frame = np.expand_dims(resized_frame, axis=-1)  # Add back channel dim
                    else:
                        resized_frame = cv2.resize(frame, (new_width, new_height))
                    resized_frames.append(resized_frame)
                frames = np.array(resized_frames)

            # Subsample frames (every 3rd frame)
            frames = frames[::3]

            # Transpose to (num_frames, channels, height, width)
            frames = np.transpose(frames, (0, 3, 1, 2))

            # Pad or truncate to max_frames
            if len(frames) > self.max_frames:
                frames = frames[:self.max_frames]
            else:
                padding_shape = (self.max_frames - len(frames), channels, frames.shape[2], frames.shape[3])
                padding = np.zeros(padding_shape, dtype=np.float32)
                frames = np.concatenate((frames, padding), axis=0)

            return frames.astype(np.float32)

        except Exception as e:
            print(f"Error processing video {video_path}: {e}")
            raise RuntimeError(f"Failed to process video: {video_path}")

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        """
        Get a sample consisting of 4 processed videos and their label.

        Returns:
            Tuple[List[torch.Tensor], int]: (list of 4 video tensors, label)
        """
        video_group, label = self.samples[idx]

        processed_videos = []
        for video_path in video_group:
            processed_video = self._process_video(video_path)
            processed_videos.append(torch.from_numpy(processed_video))

        return processed_videos, label
