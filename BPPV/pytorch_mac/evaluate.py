import torch
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
from typing import List, Tuple
import seaborn as sns
import matplotlib.pyplot as plt

print("Evaluate module loaded.")

def evaluate_model(model, data_loader, device, model_type='legacy'):
    """
    Evaluate model performance.

    Args:
        model: The model to evaluate
        data_loader: DataLoader providing evaluation data
        device: Device to run evaluation on
        model_type: 'legacy' for old models, 'bppv_transformer' for new transformer model

    Returns:
        float: Accuracy score
    """
    model.eval()
    correct, total = 0, 0

    with torch.no_grad():
        for batch_data in data_loader:
            if model_type == 'bppv_transformer':
                # New format: (list of 4 video tensors, labels)
                video_list, labels = batch_data
                video_list = [video.to(device) for video in video_list]
                labels = labels.to(device)

                outputs = model(video_list)
                _, predictions = torch.max(outputs.data, 1)
                correct += (predictions == labels).sum().item()

            else:
                # Legacy format: (videos, labels)
                videos, labels = batch_data
                videos, labels = videos.to(device), labels.to(device)
                outputs = model(videos)

                if outputs.shape[1] == 1:  # Binary classification
                    predictions = torch.round(torch.sigmoid(outputs))
                    correct += (predictions == labels.unsqueeze(1)).sum().item()
                else:  # Multi-class classification
                    _, predictions = torch.max(outputs.data, 1)
                    correct += (predictions == labels).sum().item()

            total += labels.size(0)

    return correct / total


def evaluate_four_class_model(model, data_loader, device, class_names=None,
                            save_plots=False, plot_dir='./evaluation_plots'):
    """
    Comprehensive evaluation for four-class BPPV classification.

    Args:
        model: The model to evaluate
        data_loader: DataLoader providing evaluation data
        device: Device to run evaluation on
        class_names: List of class names for reporting
        save_plots: Whether to save confusion matrix plot
        plot_dir: Directory to save plots

    Returns:
        dict: Dictionary containing evaluation metrics
    """
    if class_names is None:
        class_names = ['horizontal_left', 'horizontal_right', 'posterior_left', 'posterior_right']

    model.eval()
    all_predictions = []
    all_labels = []
    all_probabilities = []

    with torch.no_grad():
        for video_list, labels in data_loader:
            # Move data to device
            video_list = [video.to(device) for video in video_list]
            labels = labels.to(device)

            # Forward pass
            outputs = model(video_list)
            probabilities = torch.softmax(outputs, dim=1)
            _, predictions = torch.max(outputs.data, 1)

            # Collect results
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())

    # Convert to numpy arrays
    all_predictions = np.array(all_predictions)
    all_labels = np.array(all_labels)
    all_probabilities = np.array(all_probabilities)

    # Calculate accuracy
    accuracy = (all_predictions == all_labels).mean()

    # Generate classification report
    report = classification_report(
        all_labels, all_predictions,
        target_names=class_names,
        output_dict=True
    )

    # Generate confusion matrix
    cm = confusion_matrix(all_labels, all_predictions)

    # Print results
    print("=" * 60)
    print("FOUR-CLASS BPPV CLASSIFICATION EVALUATION RESULTS")
    print("=" * 60)
    print(f"Overall Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print("\nClassification Report:")
    print(classification_report(all_labels, all_predictions, target_names=class_names))

    print("\nConfusion Matrix:")
    print("Predicted ->")
    print("Actual ↓")

    # Print confusion matrix with class names
    print(f"{'':>15}", end="")
    for name in class_names:
        print(f"{name:>15}", end="")
    print()

    for i, name in enumerate(class_names):
        print(f"{name:>15}", end="")
        for j in range(len(class_names)):
            print(f"{cm[i, j]:>15}", end="")
        print()

    # Per-class accuracy
    print("\nPer-class Accuracy:")
    for i, name in enumerate(class_names):
        class_accuracy = cm[i, i] / cm[i, :].sum() if cm[i, :].sum() > 0 else 0
        print(f"  {name}: {class_accuracy:.4f} ({class_accuracy*100:.2f}%)")

    # Save confusion matrix plot if requested
    if save_plots:
        import os
        os.makedirs(plot_dir, exist_ok=True)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('Confusion Matrix - BPPV Four-Class Classification')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        plt.savefig(f'{plot_dir}/confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"\nConfusion matrix plot saved to: {plot_dir}/confusion_matrix.png")

    # Return comprehensive results
    results = {
        'accuracy': accuracy,
        'classification_report': report,
        'confusion_matrix': cm,
        'predictions': all_predictions,
        'true_labels': all_labels,
        'probabilities': all_probabilities,
        'class_names': class_names
    }

    return results


def quick_evaluate_four_class(model, data_loader, device):
    """
    Quick evaluation function that returns just the accuracy.
    Wrapper around evaluate_model for compatibility.
    """
    return evaluate_model(model, data_loader, device, model_type='bppv_transformer')
