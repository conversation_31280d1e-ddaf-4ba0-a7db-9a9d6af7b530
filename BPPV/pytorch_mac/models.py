import torch
import torch.nn as nn
import torchvision
import math
from typing import List, Optional
from transformers import ViTModel, ViTConfig

print("Models module loaded.")

class CNNLSTMModel(nn.Module):
    def __init__(self, cnn_output_size=512, lstm_hidden_size=256, lstm_num_layers=2, num_classes=1):
        super(CNNLSTMModel, self).__init__()
        # Pretrained CNN backbone
        self.cnn = torchvision.models.resnet18(pretrained=True)
        self.cnn.fc = nn.Identity()  # Remove the fully connected layer
        self.cnn_output_size = cnn_output_size

        # LSTM for temporal processing
        self.lstm = nn.LSTM(
            input_size=cnn_output_size,
            hidden_size=lstm_hidden_size,
            num_layers=lstm_num_layers,
            batch_first=True
        )

        # Fully connected layer for classification
        self.fc = nn.Linear(lstm_hidden_size, num_classes)

    def forward(self, x):
        # x: (batch_size, channels, num_frames, height, width)
        batch_size, channels, num_frames, height, width = x.size()

        # Reshape for CNN processing: (batch_size * num_frames, channels, height, width)
        x = x.permute(0, 2, 1, 3, 4).reshape(-1, channels, height, width)
        cnn_features = self.cnn(x)  # Extract features for each frame

        # Reshape for LSTM: (batch_size, num_frames, cnn_output_size)
        cnn_features = cnn_features.view(batch_size, num_frames, self.cnn_output_size)

        # Process temporal data with LSTM
        lstm_out, _ = self.lstm(cnn_features)

        # Use the last hidden state for classification
        output = self.fc(lstm_out[:, -1, :])
        return output


class BPPVTransformerModel(nn.Module):
    """
    Transformer-based model for BPPV four-class classification.
    Supports two concatenation modes:
    1. feature_concat: Concatenate features from 4 videos at each timestep
    2. separate_fusion: Process 4 videos separately then fuse features
    """

    def __init__(self, num_classes=4, concatenate_mode='feature_concat',
                 transformer_dim=768, max_frames=2000, use_pretrained=True):
        super(BPPVTransformerModel, self).__init__()

        self.num_classes = num_classes
        self.concatenate_mode = concatenate_mode
        self.transformer_dim = transformer_dim
        self.max_frames = max_frames

        # Vision Transformer configuration
        if use_pretrained:
            # Use pretrained ViT-Base
            self.vit = ViTModel.from_pretrained('google/vit-base-patch16-224')
            # Modify for grayscale input (1 channel instead of 3)
            original_embeddings = self.vit.embeddings.patch_embeddings.projection
            self.vit.embeddings.patch_embeddings.projection = nn.Conv2d(
                1, original_embeddings.out_channels,
                kernel_size=original_embeddings.kernel_size,
                stride=original_embeddings.stride
            )
        else:
            # Create ViT from scratch
            config = ViTConfig(
                hidden_size=transformer_dim,
                num_hidden_layers=12,
                num_attention_heads=12,
                intermediate_size=3072,
                num_channels=1,  # Grayscale input
                image_size=224,
                patch_size=16
            )
            self.vit = ViTModel(config)

        # Positional encoding for temporal dimension
        self.temporal_pos_encoding = nn.Parameter(
            torch.zeros(1, max_frames, transformer_dim)
        )

        if concatenate_mode == 'feature_concat':
            # Mode 1: Concatenate features from 4 videos
            self.feature_fusion = nn.Linear(transformer_dim * 4, transformer_dim)
            self.temporal_transformer = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(
                    d_model=transformer_dim,
                    nhead=12,
                    dim_feedforward=3072,
                    dropout=0.1,
                    batch_first=True
                ),
                num_layers=6
            )
        elif concatenate_mode == 'separate_fusion':
            # Mode 2: Process videos separately then fuse
            self.video_transformers = nn.ModuleList([
                nn.TransformerEncoder(
                    nn.TransformerEncoderLayer(
                        d_model=transformer_dim,
                        nhead=12,
                        dim_feedforward=3072,
                        dropout=0.1,
                        batch_first=True
                    ),
                    num_layers=4
                ) for _ in range(4)
            ])
            self.fusion_transformer = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(
                    d_model=transformer_dim,
                    nhead=12,
                    dim_feedforward=3072,
                    dropout=0.1,
                    batch_first=True
                ),
                num_layers=2
            )
        else:
            raise ValueError(f"Unknown concatenate_mode: {concatenate_mode}")

        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(transformer_dim),
            nn.Dropout(0.1),
            nn.Linear(transformer_dim, num_classes)
        )

        # Initialize positional encoding
        self._init_positional_encoding()

    def _init_positional_encoding(self):
        """Initialize sinusoidal positional encoding."""
        position = torch.arange(self.max_frames).unsqueeze(1).float()
        div_term = torch.exp(torch.arange(0, self.transformer_dim, 2).float() *
                           -(math.log(10000.0) / self.transformer_dim))

        pos_encoding = torch.zeros(self.max_frames, self.transformer_dim)
        pos_encoding[:, 0::2] = torch.sin(position * div_term)
        pos_encoding[:, 1::2] = torch.cos(position * div_term)

        self.temporal_pos_encoding.data = pos_encoding.unsqueeze(0)

    def _extract_frame_features(self, video_frames):
        """
        Extract features from video frames using ViT.

        Args:
            video_frames: (batch_size, num_frames, channels, height, width)

        Returns:
            features: (batch_size, num_frames, transformer_dim)
        """
        batch_size, num_frames, channels, height, width = video_frames.shape

        # Reshape for ViT processing
        frames_flat = video_frames.view(-1, channels, height, width)

        # Extract features using ViT
        with torch.no_grad() if self.training else torch.enable_grad():
            vit_outputs = self.vit(pixel_values=frames_flat)
            frame_features = vit_outputs.last_hidden_state[:, 0]  # Use [CLS] token

        # Reshape back to (batch_size, num_frames, transformer_dim)
        features = frame_features.view(batch_size, num_frames, -1)

        return features

    def _create_padding_mask(self, video_lengths, max_length):
        """Create padding mask for variable-length sequences."""
        batch_size = len(video_lengths)
        mask = torch.zeros(batch_size, max_length, dtype=torch.bool, device=video_lengths.device)

        for i, length in enumerate(video_lengths):
            if length < max_length:
                mask[i, length:] = True

        return mask

    def forward(self, video_list: List[torch.Tensor]):
        """
        Forward pass for BPPV classification.

        Args:
            video_list: List of 4 video tensors, each with shape (batch_size, num_frames, channels, H, W)

        Returns:
            logits: (batch_size, num_classes)
        """
        batch_size = video_list[0].shape[0]

        if self.concatenate_mode == 'feature_concat':
            # Mode 1: Feature concatenation
            all_features = []
            min_frames = min(video.shape[1] for video in video_list)

            for video in video_list:
                # Truncate to minimum length across all videos
                video_truncated = video[:, :min_frames]
                features = self._extract_frame_features(video_truncated)
                all_features.append(features)

            # Concatenate features: (batch_size, num_frames, transformer_dim * 4)
            concatenated_features = torch.cat(all_features, dim=-1)

            # Fuse concatenated features
            fused_features = self.feature_fusion(concatenated_features)

            # Add temporal positional encoding
            seq_length = fused_features.shape[1]
            pos_encoding = self.temporal_pos_encoding[:, :seq_length, :]
            fused_features = fused_features + pos_encoding

            # Create padding mask
            video_lengths = torch.full((batch_size,), seq_length, device=fused_features.device)
            padding_mask = self._create_padding_mask(video_lengths, seq_length)

            # Apply temporal transformer
            transformer_output = self.temporal_transformer(
                fused_features, src_key_padding_mask=padding_mask
            )

            # Global average pooling over non-padded positions
            pooled_features = transformer_output.mean(dim=1)

        elif self.concatenate_mode == 'separate_fusion':
            # Mode 2: Separate processing then fusion
            video_representations = []

            for i, video in enumerate(video_list):
                # Extract frame features
                features = self._extract_frame_features(video)

                # Add temporal positional encoding
                seq_length = features.shape[1]
                pos_encoding = self.temporal_pos_encoding[:, :seq_length, :]
                features = features + pos_encoding

                # Create padding mask
                video_lengths = torch.full((batch_size,), seq_length, device=features.device)
                padding_mask = self._create_padding_mask(video_lengths, seq_length)

                # Apply video-specific transformer
                video_output = self.video_transformers[i](
                    features, src_key_padding_mask=padding_mask
                )

                # Global average pooling
                video_repr = video_output.mean(dim=1)
                video_representations.append(video_repr)

            # Stack video representations: (batch_size, 4, transformer_dim)
            stacked_repr = torch.stack(video_representations, dim=1)

            # Apply fusion transformer
            fused_output = self.fusion_transformer(stacked_repr)

            # Global average pooling over videos
            pooled_features = fused_output.mean(dim=1)

        # Classification
        logits = self.classifier(pooled_features)

        return logits


def load_model(model_name="r3d", **kwargs):
    """
    Load a model based on the specified name.
    :param model_name: The name of the model. Options: ["r3d", "cnn_lstm", "bppv_transformer"]
    :param kwargs: Additional parameters for specific models.
    :return: An instantiated PyTorch model.
    """
    if model_name == "r3d":
        model = torchvision.models.video.r3d_18(pretrained=True)
        num_features = model.fc.in_features
        model.fc = nn.Linear(num_features, 1)  # Binary classification
    elif model_name == "cnn_lstm":
        model = CNNLSTMModel(**kwargs)
    elif model_name == "bppv_transformer":
        model = BPPVTransformerModel(**kwargs)
    else:
        raise ValueError(f"Unknown model name: {model_name}")
    return model
