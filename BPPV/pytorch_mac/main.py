import torch
import argparse
from torch.utils.data import DataLoader
from data import VideoDataset, BPPVFourClassDataset
from models import load_model
from train import train_model, train_four_class_model
from evaluate import evaluate_model, evaluate_four_class_model, quick_evaluate_four_class
from utils import get_device
import config

print("Main module loaded.")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='BPPV Classification Training')

    parser.add_argument('--mode', type=str, choices=['binary', 'four_class'],
                       default='four_class', help='Classification mode')
    parser.add_argument('--concatenate_mode', type=str,
                       choices=['feature_concat', 'separate_fusion'],
                       default='feature_concat',
                       help='Video concatenation mode for four-class classification')
    parser.add_argument('--model_name', type=str,
                       choices=['r3d', 'cnn_lstm', 'bppv_transformer'],
                       default='bppv_transformer', help='Model architecture')
    parser.add_argument('--batch_size', type=int, default=config.BATCH_SIZE,
                       help='Batch size for training')
    parser.add_argument('--num_epochs', type=int, default=config.NUM_EPOCHS,
                       help='Number of training epochs')
    parser.add_argument('--learning_rate', type=float, default=config.LEARNING_RATE,
                       help='Learning rate')
    parser.add_argument('--max_frames', type=int, default=config.MAX_FRAMES,
                       help='Maximum number of frames per video')
    parser.add_argument('--eval_only', action='store_true',
                       help='Only run evaluation, skip training')
    parser.add_argument('--save_plots', action='store_true',
                       help='Save evaluation plots')
    parser.add_argument('--model_path', type=str, default=None,
                       help='Path to saved model for evaluation')

    return parser.parse_args()

def main_binary_classification(args, device):
    """Run binary classification (legacy mode)."""
    print("Running binary classification mode...")

    # Load dataset
    dataset = VideoDataset(config.NORMAL_FILE_PATH, config.BPPV_FILE_PATH, args.max_frames)
    data_loader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True)

    # Load model
    if args.model_name == "cnn_lstm":
        model = load_model(args.model_name, cnn_output_size=512,
                          lstm_hidden_size=256, lstm_num_layers=2).to(device)
    else:
        model = load_model(args.model_name).to(device)

    print(f"Model selected: {args.model_name}")

    # Set up training components
    criterion = torch.nn.BCEWithLogitsLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=args.learning_rate)

    if not args.eval_only:
        # Train the model
        print("Starting training...")
        train_model(model, data_loader, device, args.num_epochs, criterion, optimizer, 'legacy')

    # Evaluate the model
    print("Starting evaluation...")
    accuracy = evaluate_model(model, data_loader, device, 'legacy')
    print(f"Model accuracy: {accuracy * 100:.2f}%")

def main_four_class_classification(args, device):
    """Run four-class classification (new mode)."""
    print("Running four-class classification mode...")
    print(f"Concatenation mode: {args.concatenate_mode}")

    # Load dataset
    dataset = BPPVFourClassDataset(
        class_files=config.CLASS_FILES,
        data_root=config.DATA_ROOT,
        max_frames=args.max_frames,
        resize_factor=config.RESIZE_FACTOR,
        convert_to_grayscale=config.CONVERT_TO_GRAYSCALE
    )

    data_loader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True)
    print(f"Dataset loaded: {len(dataset)} samples")

    # Load model
    if args.model_name == "bppv_transformer":
        model = load_model(
            args.model_name,
            num_classes=config.NUM_CLASSES,
            concatenate_mode=args.concatenate_mode,
            transformer_dim=config.TRANSFORMER_DIM,
            max_frames=args.max_frames,
            use_pretrained=config.USE_PRETRAINED_VIT
        ).to(device)
    else:
        raise ValueError(f"Model {args.model_name} not supported for four-class classification")

    print(f"Model selected: {args.model_name}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Load saved model if specified
    if args.model_path:
        print(f"Loading model from: {args.model_path}")
        model.load_state_dict(torch.load(args.model_path, map_location=device))

    if not args.eval_only:
        # Set up training components
        criterion = torch.nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=0.01)

        # Train the model
        print("Starting training...")
        train_four_class_model(model, data_loader, device, args.num_epochs, criterion, optimizer)

        # Save trained model
        model_save_path = f"bppv_transformer_{args.concatenate_mode}_epochs{args.num_epochs}.pth"
        torch.save(model.state_dict(), model_save_path)
        print(f"Model saved to: {model_save_path}")

    # Comprehensive evaluation
    print("Starting comprehensive evaluation...")
    results = evaluate_four_class_model(
        model, data_loader, device,
        class_names=config.CLASS_NAMES,
        save_plots=args.save_plots
    )

    return results

def main():
    """Main function."""
    args = parse_arguments()

    # Choose the device
    device = get_device()
    print(f"Device selected: {device}")
    print(f"Arguments: {vars(args)}")

    if args.mode == 'binary':
        main_binary_classification(args, device)
    elif args.mode == 'four_class':
        results = main_four_class_classification(args, device)

        # Print summary
        print("\n" + "="*60)
        print("TRAINING AND EVALUATION COMPLETED")
        print("="*60)
        print(f"Final Accuracy: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
        print(f"Concatenation Mode: {args.concatenate_mode}")
        print(f"Model: {args.model_name}")
        print(f"Epochs: {args.num_epochs}")
        print(f"Batch Size: {args.batch_size}")
    else:
        raise ValueError(f"Unknown mode: {args.mode}")

if __name__ == "__main__":
    main()
