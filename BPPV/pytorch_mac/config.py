MAX_FRAMES = 2000  # Maximum number of frames per video
BATCH_SIZE = 2
NUM_EPOCHS = 10
LEARNING_RATE = 0.001

# Four-class classification settings
NUM_CLASSES = 4
# Class encoding:
# 0: horizontal_left
# 1: horizontal_right
# 2: posterior_left
# 3: posterior_right
CLASS_NAMES = ['horizontal_left', 'horizontal_right', 'posterior_left', 'posterior_right']

# Data paths for four-class classification
DATA_ROOT = '/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPV/data/demo2025_07_13/BPPV_split'
CLASS_FILES = {
    'horizontal_left': f'{DATA_ROOT}/horizontal_left.txt',
    'horizontal_right': f'{DATA_ROOT}/horizontal_right.txt',
    'posterior_left': f'{DATA_ROOT}/posterior_left.txt',
    'posterior_right': f'{DATA_ROOT}/posterior_right.txt'
}

# Video processing settings
RESIZE_FACTOR = 0.1  # Resize to 1/10 of original resolution
CONVERT_TO_GRAYSCALE = True  # Convert to 1-channel grayscale

# Model settings
CONCATENATE_MODE = 'feature_concat'  # Options: 'feature_concat', 'separate_fusion'
# feature_concat: concatenate features from 4 videos at each timestep
# separate_fusion: process 4 videos separately then fuse

# Transformer settings
TRANSFORMER_DIM = 768  # Vision Transformer hidden dimension
TRANSFORMER_HEADS = 12  # Number of attention heads
TRANSFORMER_LAYERS = 12  # Number of transformer layers
USE_PRETRAINED_VIT = True  # Use pretrained Vision Transformer

# Legacy paths (kept for compatibility)
NORMAL_FILE_PATH = '/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPVVideos/preprocessing/video_list.txt'
BPPV_FILE_PATH = '/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/BPPVVideos/preprocessing/video_list.txt'
