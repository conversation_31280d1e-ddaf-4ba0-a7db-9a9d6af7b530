Test name,Eye,Direction,Begin,End,aSPV,mSPV,BeatCount,BeatsPrSecond,OppositeDirectionBeatCount,Trace,SetByUser,Label
Center,Left,Up,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Center,Left,Down,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Center,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Center,Left,Right,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Left,Left,Up,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Left,Left,Down,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,False,
Left,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Left,Left,Right,0,0,NaN,NaN,-1,0,-1,<PERSON><PERSON>,<PERSON>als<PERSON>,
Right,Left,Up,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,
Right,Left,Down,0,0,<PERSON><PERSON>,<PERSON><PERSON>,-1,0,-1,<PERSON><PERSON>,False,
Right,Left,Left,0,0,<PERSON>N,<PERSON><PERSON>,-1,0,-1,<PERSON>tal,False,
Right,Left,Right,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Up,Left,Up,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Up,Left,Down,0,0,NaN,NaN,-1,0,-1,<PERSON>tal,False,
Up,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Up,Left,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Up,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Down,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Left,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
Down,Left,Right,0,0,NaN,NaN,-1,0,-1,Horizontal,False,
